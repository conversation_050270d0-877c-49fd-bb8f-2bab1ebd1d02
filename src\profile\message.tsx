import ProfileLayout from './ProfileLayout';
import { useState } from 'react';

export default function Message() {
  const [selectedMessage, setSelectedMessage] = useState<number | null>(1);

  const messages = [
    {
      id: 1,
      sender: "<PERSON>",
      subject: "Interested in your listing",
      preview: "Hi, I'm interested in your product...",
      time: "2 hours ago",
      unread: true,
      avatar: "JS"
    },
    {
      id: 2,
      sender: "<PERSON>",
      subject: "Question about shipping",
      preview: "Could you please clarify the shipping...",
      time: "1 day ago",
      unread: false,
      avatar: "SJ"
    },
    {
      id: 3,
      sender: "<PERSON>",
      subject: "Payment confirmation",
      preview: "Thank you for your purchase...",
      time: "3 days ago",
      unread: false,
      avatar: "MW"
    },
  ];

  const currentMessage = messages.find(m => m.id === selectedMessage);

  return (
    <ProfileLayout
      title="Messages"
      subtitle="Communicate with buyers and sellers"
    >
      <div className="flex h-[600px] bg-white rounded-xl border border-gray-200 overflow-hidden">
        {/* Messages List */}
        <div className="w-1/3 border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Inbox</h3>
              <button className="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                Compose
              </button>
            </div>
            <div className="relative">
              <input
                type="text"
                placeholder="Search messages..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <svg className="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            {messages.map((message) => (
              <div
                key={message.id}
                onClick={() => setSelectedMessage(message.id)}
                className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${
                  selectedMessage === message.id ? 'bg-blue-50 border-blue-200' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                    {message.avatar}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className={`text-sm font-medium ${message.unread ? 'text-gray-900' : 'text-gray-600'}`}>
                        {message.sender}
                      </p>
                      <p className="text-xs text-gray-500">{message.time}</p>
                    </div>
                    <p className={`text-sm ${message.unread ? 'font-medium text-gray-900' : 'text-gray-600'} truncate`}>
                      {message.subject}
                    </p>
                    <p className="text-xs text-gray-500 truncate mt-1">{message.preview}</p>
                    {message.unread && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Message Content */}
        <div className="flex-1 flex flex-col">
          {currentMessage ? (
            <>
              {/* Message Header */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                      {currentMessage.avatar}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{currentMessage.sender}</h3>
                      <p className="text-sm text-gray-500">{currentMessage.time}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                      </svg>
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mt-4">{currentMessage.subject}</h2>
              </div>

              {/* Message Body */}
              <div className="flex-1 p-6 overflow-y-auto">
                <div className="prose max-w-none">
                  <p className="text-gray-700 leading-relaxed">
                    Hi there,<br/><br/>
                    I hope this message finds you well. I'm very interested in your product listing and would like to know more details about it.
                    Could you please provide more information about the condition, availability, and any additional features?<br/><br/>
                    I'm particularly interested in:
                  </p>
                  <ul className="list-disc list-inside text-gray-700 mt-4 space-y-1">
                    <li>Current condition of the item</li>
                    <li>Shipping options and costs</li>
                    <li>Return policy</li>
                    <li>Any warranty information</li>
                  </ul>
                  <p className="text-gray-700 mt-4">
                    Please let me know if you have any questions or if there's anything else I should know before making a decision.
                  </p>
                  <p className="text-gray-700 mt-4">
                    Thank you for your time and I look forward to hearing from you soon.
                  </p>
                  <p className="text-gray-700 mt-4">
                    Best regards,<br/>
                    {currentMessage.sender}
                  </p>
                </div>
              </div>

              {/* Reply Section */}
              <div className="p-6 border-t border-gray-200">
                <div className="space-y-4">
                  <textarea
                    placeholder="Type your reply..."
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  />
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-2">
                      <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                      </button>
                      <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                        </svg>
                      </button>
                    </div>
                    <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                      Send Reply
                    </button>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p className="text-gray-500">Select a message to view</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </ProfileLayout>
  );
}
