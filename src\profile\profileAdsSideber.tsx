import { FC } from "react";

const menuItems = [
  {
    label: "Dashboard",
    icon: (
      <svg
        className="w-6 h-6 mr-2"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M3.75 3v11.25A2.25 2.25 0 006 16.5h12A2.25 2.25 0 0020.25 14.25V3m-16.5 0h16.5m-16.5 0v1.5A2.25 2.25 0 005.25 6h13.5A2.25 2.25 0 0020.25 4.5V3"
        />
      </svg>
    ),
  },
  {
    label: "My Ads",
    icon: (
      <svg
        className="w-6 h-6 mr-2"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      </svg>
    ),
  },
  {
    label: "Create Ad",
    icon: (
      <svg
        className="w-6 h-6 mr-2"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    ),
  },
  {
    label: "Analytics",
    icon: (
      <svg
        className="w-6 h-6 mr-2"
        fill="none"
        stroke="currentColor"
        strokeWidth="1.5"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M3.375 3h17.25c.621 0 1.125.504 1.125 1.125v17.25c0 .621-.504 1.125-1.125 1.125H3.375c-.621 0-1.125-.504-1.125-1.125V4.125c0-.621.504-1.125 1.125-1.125z"
        />
      </svg>
    ),
  },
];

const ProfileAdsSidebar: FC = () => (
  <nav
    className="h-full w-64 bg-[#0A172E] p-6 rounded-lg flex flex-col gap-4"
    aria-label="Sidebar navigation"
  >
    <ul className="space-y-4">
      {menuItems.map((item) => (
        <li
          key={item.label}
          tabIndex={0}
          aria-label={item.label}
          className="flex items-center text-gray-200 hover:text-white cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-400 rounded px-2 py-2 transition-colors"
        >
          {item.icon}
          <span className="text-lg">{item.label}</span>
        </li>
      ))}
    </ul>
  </nav>
);

export default ProfileAdsSidebar;