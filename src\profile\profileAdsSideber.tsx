import { FC, useState } from "react";

interface AdsMenuItem {
  label: string;
  icon: React.ReactElement;
  action?: () => void;
  badge?: number;
  status?: 'active' | 'pending' | 'paused';
}

const menuItems: AdsMenuItem[] = [
  {
    label: "Dashboard",
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" />
      </svg>
    ),
    status: 'active'
  },
  {
    label: "My Ads",
    badge: 12,
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
      </svg>
    ),
    status: 'active'
  },
  {
    label: "Create Ad",
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
      </svg>
    ),
    status: 'active'
  },
  {
    label: "Analytics",
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
      </svg>
    ),
    status: 'active'
  },
];

const quickActions = [
  {
    label: "Quick Boost",
    icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
    color: "from-yellow-500 to-orange-500"
  },
  {
    label: "Pause All",
    icon: (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    color: "from-red-500 to-pink-500"
  }
];

interface ProfileAdsSidebarProps {
  className?: string;
}

const ProfileAdsSidebar: FC<ProfileAdsSidebarProps> = ({ className = "" }) => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [activeItem, setActiveItem] = useState<string>("Dashboard");

  const handleItemClick = (item: AdsMenuItem) => {
    setActiveItem(item.label);
    if (item.action) {
      item.action();
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'pending': return 'bg-yellow-500';
      case 'paused': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <nav
      className={`h-fit min-h-[600px] w-72 bg-gradient-to-br from-[#1A2332] via-[#0F1B2E] to-[#0A172E] p-6 rounded-2xl shadow-2xl border border-gray-700/30 ${className}`}
      aria-label="Ads management navigation"
    >
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-white mb-2">Ads Manager</h2>
        <div className="w-12 h-1 bg-gradient-to-r from-green-500 to-blue-600 rounded-full"></div>
      </div>

      {/* Stats Overview */}
      <div className="mb-6 p-4 bg-gray-800/50 rounded-xl border border-gray-600/30">
        <div className="grid grid-cols-2 gap-3 text-center">
          <div>
            <div className="text-2xl font-bold text-green-400">12</div>
            <div className="text-xs text-gray-400">Active Ads</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">$2.4k</div>
            <div className="text-xs text-gray-400">This Month</div>
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <ul className="space-y-2 mb-6">
        {menuItems.map((item) => {
          const active = activeItem === item.label;
          const hovered = hoveredItem === item.label;

          return (
            <li key={item.label}>
              <button
                onClick={() => handleItemClick(item)}
                onMouseEnter={() => setHoveredItem(item.label)}
                onMouseLeave={() => setHoveredItem(null)}
                className={`
                  w-full flex items-center justify-between p-4 rounded-xl transition-all duration-300 ease-in-out
                  ${active
                    ? 'bg-gradient-to-r from-green-600 to-blue-600 text-white shadow-lg transform scale-[1.02]'
                    : hovered
                    ? 'bg-gray-700/50 text-white transform translate-x-2'
                    : 'text-gray-300 hover:text-white'
                  }
                  focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2 focus:ring-offset-[#1A2332]
                  group
                `}
                aria-label={item.label}
                aria-current={active ? 'page' : undefined}
              >
                <div className="flex items-center">
                  <div className={`
                    mr-4 transition-transform duration-300
                    ${active ? 'scale-110' : hovered ? 'scale-105' : ''}
                  `}>
                    {item.icon}
                  </div>
                  <span className="font-medium text-sm">{item.label}</span>
                </div>

                <div className="flex items-center space-x-2">
                  {/* Status indicator */}
                  {item.status && (
                    <div className={`w-2 h-2 rounded-full ${getStatusColor(item.status)}`}></div>
                  )}

                  {/* Badge */}
                  {item.badge && (
                    <span className="bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full min-w-[20px] h-5 flex items-center justify-center">
                      {item.badge}
                    </span>
                  )}

                  {/* Arrow indicator */}
                  {(active || hovered) && (
                    <svg
                      className={`w-4 h-4 transition-transform duration-300 ${active ? 'text-white' : 'text-gray-400'}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                    </svg>
                  )}
                </div>
              </button>
            </li>
          );
        })}
      </ul>

      {/* Quick Actions */}
      <div className="mb-6">
        <h3 className="text-sm font-semibold text-gray-400 mb-3">Quick Actions</h3>
        <div className="space-y-2">
          {quickActions.map((action) => (
            <button
              key={action.label}
              className={`
                w-full flex items-center p-3 rounded-lg bg-gradient-to-r ${action.color}
                text-white font-medium text-sm transition-all duration-300
                hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white/50
              `}
            >
              {action.icon}
              <span className="ml-2">{action.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div className="pt-6 border-t border-gray-600/30">
        <div className="text-xs text-gray-400 text-center">
          Ads Dashboard
        </div>
      </div>
    </nav>
  );
};

export default ProfileAdsSidebar;