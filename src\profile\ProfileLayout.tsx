import { FC, ReactNode } from "react";
import ProfileSidebar from './profileSideber';
import ProfileAdsSidebar from './profileAdsSideber';

interface ProfileLayoutProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
  className?: string;
}

const ProfileLayout: FC<ProfileLayoutProps> = ({ 
  children, 
  title, 
  subtitle,
  className = "" 
}) => {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 ${className}`}>
      <div className="container mx-auto px-4 py-6">
        <div className="flex gap-6">
          {/* Left Sidebar - Profile Navigation */}
          <div className="flex-shrink-0">
            <ProfileSidebar />
          </div>

          {/* Main Content Area */}
          <div className="flex-1 min-w-0">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6">
                <h1 className="text-3xl font-bold text-white mb-2">{title}</h1>
                {subtitle && (
                  <p className="text-blue-100 text-lg">{subtitle}</p>
                )}
              </div>

              {/* Content */}
              <div className="p-8">
                {children}
              </div>
            </div>
          </div>

          {/* Right Sidebar - Ads Management */}
          <div className="flex-shrink-0">
            <ProfileAdsSidebar />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileLayout;
