import { createBrowserRouter, Navigate } from "react-router-dom";
import App from "../App";

// Main Pages
import Login from "../pages/login";
import Signup from "../pages/signup";
import Home from "../pages/home";
import TrustedSeller from "../pages/trustedSeller";
import AutoEscrow from "../pages/auto-escrow";
import News from "../pages/news";

// Profile Pages
import Profile from "../profile/profile";
import AccountSecurity from "../profile/accountSecurity";
import Message from "../profile/message";
import MyStore from "../profile/mystore";
import Walletpage from "../profile/walletpage";
import Purchase from "../profile/purchase";

// Error Pages
import NotFound from "../pages/NotFound";

const router = createBrowserRouter([
    {
        path: "/",
        element: <App />,
        children: [
            // Default redirect to home
            {
                index: true,
                element: <Navigate to="/home" replace />,
            },
            // Authentication Routes
            {
                path: "login",
                element: <Login />,
            },
            {
                path: "signup",
                element: <Signup />,
            },
            // Main Application Routes
            {
                path: "home",
                element: <Home />,
            },
            {
                path: "trusted-seller",
                element: <TrustedSeller />,
            },
            {
                path: "auto-escrow",
                element: <AutoEscrow />,
            },
            {
                path: "news",
                element: <News />,
            },
            // Profile Routes
            {
                path: "profile",
                element: <Profile />,
            },
            {
                path: "account-security",
                element: <AccountSecurity />,
            },
            {
                path: "message",
                element: <Message />,
            },
            {
                path: "mystore",
                element: <MyStore />,
            },
            {
                path: "walletpage",
                element: <Walletpage />,
            },
            {
                path: "purchase",
                element: <Purchase />,
            },
            // Catch-all route for 404 pages
            {
                path: "*",
                element: <NotFound />,
            },
        ]
    },
]);

export default router;