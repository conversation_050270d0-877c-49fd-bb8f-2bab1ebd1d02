import { useState } from 'react';
import AdCard from '@/components/AdCard';

// Define type for view selection props
type ViewSelectorProps = {
  setActiveView: (view: string) => void;
}

// Main App Component
export default function App() {
  // State to manage which content view is active
  const [activeView, setActiveView] = useState('home');

  // A simple mapping of views to their content components
  const renderContent = () => {
    switch (activeView) {
      case 'create':
        return <CreateDealView />;
      case 'active':
        return <ActiveDealsView />;
      case 'archive':
        return <ArchiveDealsView />;
      case 'terms':
        return <TermsAndConditionsView />;
      default:
        return <HomeView setActiveView={setActiveView} />;
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 font-sans text-slate-300 p-4 sm:p-6 lg:p-8">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Main Content */}
          <main className="w-full flex flex-col xl:flex-row gap-8">
            {/* Content Area */}
            <div className="flex-grow">
              {activeView !== 'home' && (
                <button
                  onClick={() => setActiveView('home')}
                  className="mb-6 text-slate-400 hover:text-white transition-colors duration-300 flex items-center gap-2 bg-slate-800 p-2 rounded-lg"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m15 18-6-6 6-6"/></svg>
                  Back to Home
                </button>
              )}
              {renderContent()}
            </div>

            {/* Ads Section - Same positioning as trustedSeller.tsx */}
            <div className="w-full xl:w-1/4 flex-shrink-0 flex flex-col sm:flex-row xl:flex-col gap-8">
              <div className="w-full"><AdCard /></div>
              <div className="w-full"><AdCard /></div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}

// Home View Component (The main screen from the image)
const HomeView = ({ setActiveView }: ViewSelectorProps) => (
  <div className="flex flex-col items-center justify-center animate-fade-in mt-12">
    <h1 className="text-5xl md:text-7xl font-bold text-white tracking-wider uppercase">
      AUTO-ESCROW
    </h1>
    <p className="mt-4 text-lg md:text-xl text-slate-300 tracking-widest uppercase">
      MAKE YOUR WORK EASILY
    </p>
    <div className="mt-12 flex flex-col sm:flex-row items-center gap-4">
      <button 
        onClick={() => setActiveView('create')}
        className="w-full sm:w-auto bg-transparent border border-slate-600 hover:border-slate-400 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105"
      >
        Create a deal
      </button>
      <button 
        onClick={() => setActiveView('active')}
        className="w-full sm:w-auto bg-transparent border border-slate-600 hover:border-slate-400 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105"
      >
        Active deals
      </button>
      <button 
        onClick={() => setActiveView('archive')}
        className="w-full sm:w-auto bg-transparent border border-slate-600 hover:border-slate-400 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105"
      >
        Archive deals
      </button>
    </div>
    <div className="mt-8">
      <button 
        onClick={() => setActiveView('terms')}
        className="bg-transparent border border-slate-700 hover:border-slate-500 text-slate-400 hover:text-white font-medium py-3 px-8 rounded-lg transition-all duration-300 ease-in-out"
      >
        Auto-escrow terms and conditions
      </button>
    </div>
  </div>
);

// Placeholder for "Create a Deal" view
const CreateDealView = () => (
  <div className="text-white animate-fade-in">
    <h2 className="text-4xl font-bold mb-6">Create a New Deal</h2>
    <p className="text-slate-300">This is where the form to create a new deal would be.</p>
  </div>
);

// Placeholder for "Active Deals" view
const ActiveDealsView = () => (
  <div className="text-white animate-fade-in">
    <h2 className="text-4xl font-bold mb-6">Your Active Deals</h2>
    <p className="text-slate-300">A list of your current active deals would appear here.</p>
  </div>
);

// Placeholder for "Archive Deals" view
const ArchiveDealsView = () => (
  <div className="text-white animate-fade-in">
    <h2 className="text-4xl font-bold mb-6">Archived Deals</h2>
    <p className="text-slate-300">Previously completed or archived deals would be listed here.</p>
  </div>
);

// Placeholder for "Terms and Conditions" view
const TermsAndConditionsView = () => (
    <div className="text-left max-w-2xl mx-auto text-white animate-fade-in">
        <h2 className="text-4xl font-bold mb-6 text-center">Terms and Conditions</h2>
        <div className="space-y-4 text-slate-300">
            <p>This is a placeholder for the Auto-Escrow Terms and Conditions. In a real application, this section would contain the full legal terms governing the use of the service.</p>
            <h3 className="text-2xl font-semibold text-white pt-4">1. Introduction</h3>
            <p>Welcome to Auto-Escrow. These terms and conditions outline the rules and regulations for the use of our services.</p>
            <h3 className="text-2xl font-semibold text-white pt-4">2. Service Description</h3>
            <p>Auto-Escrow provides a secure platform for holding funds in a transaction between two parties until specified conditions are met.</p>
            <h3 className="text-2xl font-semibold text-white pt-4">3. User Responsibilities</h3>
            <p>Users are responsible for providing accurate information and for complying with all applicable laws in connection with their use of the service. Any misuse of the platform may result in suspension or termination of your account.</p>
        </div>
    </div>
);
