import { createBrowserRouter } from "react-router-dom";
import Login from "../pages/login";
import Signup from "../pages/signup";
import App from "../App";
import Home from "../pages/home";
import TrustedSeller from "../pages/trustedSeller";
import AutoEscrow from "../pages/auto-escrow";
import News from "@/pages/news";
import Profile from "@/profile/profile";
import AccountSecurity from "@/profile/AccountSecurity";
import Message from "@/profile/message";
import MyStore from "@/profile/mystore";
import Walletpage from "@/profile/Walletpage";
import Purchase from "@/profile/Purchase";

const router = createBrowserRouter([
    {
        path: "/",
        element: <App />,
        children : [
            {
                path: "/login",
                element: <Login />,
            },
            {
                path: "/signup",
                element: <Signup />,
            },
            {
                path: "/home",
                element: <Home />,
            },
            {
                path: "/trusted-seller",
                element: <TrustedSeller />,
            },
            {
                path: "/auto-escrow",
                element: <AutoEscrow />,
            },
            {
                path: "/news",
                element: <News />,
            },
            {
                path: "/profile",
                element: <Profile />,
            },
            {
                path: "/account-security",
                element: <AccountSecurity />,
            },
            {
                path: "/message",
                element: <Message />,
            },
            {
                path: "/mystore",
                element: <MyStore />,
            },
            {
                path: "/walletpage",
                element: <Walletpage />,
            },
            {
                path: "/purchase",
                element: <Purchase />,
            },
        ]
    },

])

export default router