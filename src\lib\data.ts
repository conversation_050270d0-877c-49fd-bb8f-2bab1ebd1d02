import { LayoutGrid, Shirt, Home as HomeIcon, Dumbbell, HeartP<PERSON>, Puzzle, Book } from 'lucide-react';
import React from 'react';

interface Category {
  name: string;
  icon: React.ReactNode;
}

// Mock Data for sidebar categories
export const categories: Category[] = [
  { name: 'Electronics', icon: <LayoutGrid size={20} /> },
  { name: 'Fashion', icon: <Shirt size={20} /> },
  { name: 'Home & Kitchen', icon: <HomeIcon size={20} /> },
  { name: 'Sports & Outdoors', icon: <Dumbbell size={20} /> },
  { name: 'Beauty & Health', icon: <HeartPulse size={20} /> },
  { name: 'Toys & Games', icon: <Puzzle size={20} /> },
  { name: 'Books', icon: <Book size={20} /> },
];

export const categoryIconMap: { [key: string]: React.ComponentType<{ size: number; className?: string }> } = {
  Electronics: LayoutGrid,
  Fashion: Shirt,
  'Home & Kitchen': HomeIcon,
  'Sports & Outdoors': <PERSON><PERSON><PERSON>,
  'Beauty & Health': HeartPulse,
  'Toys & Games': Puzzle,
  Books: Book,
}; 