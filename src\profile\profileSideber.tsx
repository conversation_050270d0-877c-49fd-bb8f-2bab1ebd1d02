import { FC } from "react";

const menuItems = [
  { label: "Personal Account", icon: (
    <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.5 19.25a7.5 7.5 0 1115 0v.25a2.25 2.25 0 01-2.25 2.25h-10.5A2.25 2.25 0 014.5 19.5v-.25z" /></svg>
  ) },
  { label: "Purchase", icon: (
    <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M2.25 6.75h19.5M6.75 6.75V5.25A2.25 2.25 0 019 3h6a2.25 2.25 0 012.25 2.25v1.5m-12 0h12m-12 0v12A2.25 2.25 0 008.25 21h7.5A2.25 2.25 0 0018 18.75v-12" /></svg>
  ) },
  { label: "Account Security", icon: (
    <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M16.5 10.5V6.75A4.5 4.5 0 008.25 6.75v3.75m8.25 0v6.75a4.5 4.5 0 01-8.25 0V10.5m8.25 0h-8.25" /></svg>
  ) },
  { label: "Messages", icon: (
    <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M21.75 6.75v10.5A2.25 2.25 0 0119.5 19.5h-15A2.25 2.25 0 012.25 17.25V6.75A2.25 2.25 0 014.5 4.5h15a2.25 2.25 0 012.25 2.25z" /></svg>
  ) },
  { label: "Wallet", icon: (
    <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M2.25 7.5v9A2.25 2.25 0 004.5 18.75h15a2.25 2.25 0 002.25-2.25v-9A2.25 2.25 0 0019.5 5.25h-15A2.25 2.25 0 002.25 7.5z" /></svg>
  ) },
  { label: "My Store", icon: (
    <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M3 7.5V6a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 6v1.5M3 7.5h18M3 7.5v9A2.25 2.25 0 005.25 18.75h13.5A2.25 2.25 0 0021 16.5v-9" /></svg>
  ) },
];

const Sidebar: FC = () => (
  <nav
    className="h-full w-64 bg-[#0A172E] p-6 rounded-lg flex flex-col gap-4"
    aria-label="Sidebar navigation"
  >
    <ul className="space-y-4">
      {menuItems.map((item, idx) => (
        <li
          key={item.label}
          tabIndex={0}
          aria-label={item.label}
          className="flex items-center text-gray-200 hover:text-white cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-400 rounded px-2 py-2 transition-colors"
        >
          {item.icon}
          <span className="text-lg">{item.label}</span>
        </li>
      ))}
    </ul>
  </nav>
);

export default Sidebar; 