import { useState } from 'react';
import { Mail, Phone, Star, ShieldCheck, Info, ExternalLink, Menu, X } from 'lucide-react';
import Sidebar from '../components/Sidebar';
import AdCard from '../components/AdCard';

// Define Seller interface
interface Seller {
  id: number;
  name: string;
  avatar: string;
  rating: number;
  reviews: number;
  memberSince: string;
  sales: number;
  verified: boolean;
  specialization: string[];
  description: string;
  email: string;
  phone: string;
  website?: string;
}

// Mock Data for trusted sellers
const trustedSellers: Seller[] = [
  {
    id: 1,
    name: '<PERSON>',
    avatar: 'https://i.pravatar.cc/150?img=1',
    rating: 4.9,
    reviews: 124,
    memberSince: '2018-05-12',
    sales: 542,
    verified: true,
    specialization: ['Electronics', 'Computers'],
    description: 'Specialized in high-end electronics and computer parts. Fast shipping and excellent customer service.',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'alexjtech.com'
  },
  {
    id: 2,
    name: '<PERSON>',
    avatar: 'https://i.pravatar.cc/150?img=5',
    rating: 4.8,
    reviews: 98,
    memberSince: '2019-10-15',
    sales: 317,
    verified: true,
    specialization: ['Fashion', 'Accessories'],
    description: 'Fashion enthusiast with a unique collection of designer items. All items are authentic.',
    email: '<EMAIL>',
    phone: '+****************'
  },
  {
    id: 3,
    name: 'Michael Chen',
    avatar: 'https://i.pravatar.cc/150?img=3',
    rating: 5.0,
    reviews: 76,
    memberSince: '2020-01-22',
    sales: 189,
    verified: true,
    specialization: ['Books', 'Collectibles'],
    description: 'Rare book collector specializing in first editions and signed copies. Carefully packaged shipments.',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'chenrarebooks.net'
  },
  {
    id: 4,
    name: 'Emily Rodriguez',
    avatar: 'https://i.pravatar.cc/150?img=9',
    rating: 4.7,
    reviews: 52,
    memberSince: '2021-03-08',
    sales: 93,
    verified: false,
    specialization: ['Beauty', 'Organic'],
    description: 'Passionate about organic beauty products. All items are cruelty-free and eco-friendly.',
    email: '<EMAIL>',
    phone: '+****************'
  },
  {
    id: 5,
    name: 'David Lee',
    avatar: 'https://i.pravatar.cc/150?img=7',
    rating: 4.9,
    reviews: 210,
    memberSince: '2017-11-20',
    sales: 850,
    verified: true,
    specialization: ['Home Goods', 'Furniture'],
    description: 'Curated collection of modern and minimalist home decor and furniture.',
    email: '<EMAIL>',
    phone: '+****************',
  },
  {
    id: 6,
    name: 'Jessica Garcia',
    avatar: 'https://i.pravatar.cc/150?img=8',
    rating: 4.8,
    reviews: 155,
    memberSince: '2020-02-14',
    sales: 420,
    verified: true,
    specialization: ['Handmade', 'Crafts'],
    description: 'Unique handmade crafts, perfect for gifts and special occasions.',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'jesscrafts.com'
  },
];

// Redesigned Seller Card Component
const SellerCard = ({ seller }: { seller: Seller }) => {
  return (
    <div className="relative bg-gray-800/50 backdrop-blur-sm border border-gray-700/80 rounded-2xl overflow-hidden group transition-all duration-300 hover:shadow-2xl hover:shadow-teal-500/10 hover:border-teal-500/60">
      {/* Header section */}
      <div className="p-5">
        <div className="flex items-center space-x-4">
          <div className="relative flex-shrink-0">
            <img 
              src={seller.avatar} 
              alt={seller.name} 
              className="w-16 h-16 rounded-full object-cover border-2 border-gray-600 group-hover:border-teal-400 transition-colors duration-300"
            />
            {seller.verified && (
              <div className="absolute -bottom-1 -right-1 bg-teal-500 text-white p-1 rounded-full ring-2 ring-gray-800" title="Verified Seller">
                <ShieldCheck size={16} />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-bold text-white text-lg truncate" title={seller.name}>{seller.name}</h3>
            <div className="flex items-center mt-1">
              <Star className="text-yellow-400 fill-yellow-400" size={16} />
              <span className="text-white font-semibold ml-1.5">{seller.rating}</span>
              <span className="text-gray-400 text-sm ml-2">({seller.reviews} reviews)</span>
            </div>
          </div>
        </div>
      </div>

      {/* Body section */}
      <div className="px-5 pb-5">
        <p className="text-gray-300 text-sm mb-4 h-10">{seller.description.substring(0, 80)}{seller.description.length > 80 ? '...' : ''}</p>
        
        <div className="flex flex-wrap gap-2">
          {seller.specialization.map(spec => (
            <span key={spec} className="bg-gray-700 text-teal-300 text-xs font-medium px-2.5 py-1 rounded-full">
              {spec}
            </span>
          ))}
        </div>
      </div>

      {/* Footer Actions */}
      <div className="bg-gray-900/50 px-5 py-3 flex justify-between items-center border-t border-gray-700/50">
        <div className="flex space-x-3">
          <a href={`mailto:${seller.email}`} className="text-gray-400 hover:text-white transition-colors" aria-label="Email seller" title="Email">
            <Mail size={20} />
          </a>
          <a href={`tel:${seller.phone}`} className="text-gray-400 hover:text-white transition-colors" aria-label="Call seller" title="Call">
            <Phone size={20} />
          </a>
          {seller.website && (
            <a href={`https://${seller.website}`} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors" aria-label="Visit seller's website" title="Website">
              <ExternalLink size={20} />
            </a>
          )}
        </div>
        <button className="text-sm font-semibold text-teal-400 hover:text-white flex items-center gap-1.5 transition-colors" aria-label={`View ${seller.name}'s full profile`}>
          <span>Profile</span>
          <Info size={16} />
        </button>
      </div>
    </div>
  );
};

// Main TrustedSeller Component with new design
export default function TrustedSeller() {
  const [isSidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-900 font-sans text-gray-300 p-4 sm:p-6 lg:p-8">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row gap-8">
          
          {/* Mobile Header with Hamburger Menu */}
          <div className="md:hidden flex justify-between items-center mb-4">
            <h1 className="text-xl font-bold text-white">Trusted Sellers</h1>
            <button 
              onClick={() => setSidebarOpen(!isSidebarOpen)} 
              className="text-white z-20"
              aria-label={isSidebarOpen ? "Close sidebar" : "Open sidebar"}
              tabIndex={0}
            >
              {isSidebarOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
          
          {/* Sidebar */}
          <div className={`md:block ${isSidebarOpen ? 'block' : 'hidden'}`}>
            <Sidebar isOpen={isSidebarOpen} />
          </div>

          {/* Main Content */}
          <main className="w-full flex flex-col xl:flex-row gap-8">
            {/* Seller List */}
            <div className="flex-grow">
              {/* Header */}
              <header className="mb-6">
                <h1 className="text-2xl sm:text-3xl font-bold text-white">Trusted Sellers</h1>
                <p className="text-gray-400 mt-2">Discover our top-rated and verified partners.</p>
              </header>

              {/* Seller Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {trustedSellers.map((seller) => (
                  <SellerCard key={seller.id} seller={seller} />
                ))}
              </div>
            </div>

            {/* Ads Section */}
            <div className="w-full xl:w-1/4 flex-shrink-0 flex flex-col sm:flex-row xl:flex-col gap-8">
              <div className="w-full"><AdCard /></div>
              <div className="w-full"><AdCard /></div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
