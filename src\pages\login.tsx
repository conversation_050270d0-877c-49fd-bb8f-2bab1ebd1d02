import React, { useState } from 'react';
import { Link } from 'react-router-dom';

// --- SVG Icon Components ---

type IconProps = React.SVGProps<SVGSVGElement>;

interface EyeIconProps extends IconProps {
  a11yTitle?: string;
}

const UserIcon = (props: IconProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
    <circle cx="12" cy="7" r="4"></circle>
  </svg>
);

const LockIcon = (props: IconProps) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
    </svg>
);

const EyeIcon = ({ a11yTitle, ...props }: EyeIconProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
    <title>{a11yTitle}</title>
    <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
    <circle cx="12" cy="12" r="3" />
  </svg>
);

const EyeOffIcon = ({ a11yTitle, ...props }: EyeIconProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}>
    <title>{a11yTitle}</title>
    <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24" />
    <path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68" />
    <path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61" />
    <line x1="2" x2="22" y1="2" y2="22" />
  </svg>
);

// --- Main App Component ---
export default function Login() {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-[#0A172E] p-4 font-sans">
      <div className="w-full max-w-md mx-auto">
        
        {/* Logo and Title */}
        <div className="text-center mb-8">
            <h1 className="text-white text-6xl md:text-7xl font-extrabold tracking-tighter mb-2">
              RMARKET
            </h1>
            <p className="text-sm tracking-widest">
              <span className="text-slate-400">INNOVATION </span>
              <span className="text-white font-semibold">MARKETPLACE</span>
            </p>
        </div>

        {/* Login Card */}
        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl shadow-2xl p-8">
          <div className="text-center mb-8">
            <h2 className="text-white text-2xl font-bold">Sign In</h2>
            <p className="text-slate-400 text-sm mt-1">Enter your credentials to access your account</p>
          </div>
          
          <form className="space-y-6">
            {/* Username Input */}
            <div className="relative">
              <UserIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-slate-400"/>
              <input
                type="text"
                placeholder="Username"
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 py-3 pl-12 pr-4 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
              />
            </div>
            
            {/* Password Input */}
            <div className="relative">
              <LockIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-slate-400"/>
              <input
                type={showPassword ? 'text' : 'password'}
                placeholder="Password"
                className="w-full bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-slate-400 py-3 pl-12 pr-12 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? <EyeIcon a11yTitle="Hide password" /> : <EyeOffIcon a11yTitle="Show password" />}
              </button>
            </div>
            
            <div className="text-right">
                <a href="#" className="text-sm text-cyan-400 hover:text-cyan-300 hover:underline">
                    Forgot Password?
                </a>
            </div>

            {/* Login Button */}
            <button
              type="submit"
              className="w-full bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg shadow-cyan-500/20"
            >
              Login
            </button>
          </form>
          
          {/* Sign Up Link */}
          <p className="text-center text-slate-400 mt-8">
            Don't have an account?{' '}
            <Link to="/signup" className="font-semibold text-cyan-400 hover:text-cyan-300 hover:underline">
              Sign Up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
