import { CalendarIcon, UserIcon, ExternalLinkIcon, ChevronRightIcon, SearchIcon } from 'lucide-react';
import AdCard from '../components/AdCard';
import { useState } from 'react';

// Define News interface
interface NewsItem {
  id: number;
  title: string;
  summary: string;
  content: string;
  image: string;
  date: string;
  author: string;
  category: string;
  url?: string;
}

// Local fallback images (using consistent placeholder patterns)
const fallbackImages = [
  "https://placehold.co/800x400/1f2937/teal?text=Market+Trends",
  "https://placehold.co/800x400/1f2937/teal?text=Security",
  "https://placehold.co/800x400/1f2937/teal?text=Payments",
  "https://placehold.co/800x400/1f2937/teal?text=Seller+Tips",
  "https://placehold.co/800x400/1f2937/teal?text=Technology",
  "https://placehold.co/800x400/1f2937/teal?text=Policy",
];

// Mock data for news
const newsItems: NewsItem[] = [
  {
    id: 1,
    title: "New Market Trends in Digital Goods Trading",
    summary: "Discover the latest trends affecting digital marketplace economics.",
    content: "The digital marketplace landscape has evolved significantly over the past year, with NFTs and digital collectibles leading the charge. Experts predict that the next wave will focus on practical digital goods with real-world utility.",
    image: "https://images.unsplash.com/photo-1518770660439-4636190af475?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
    date: "2023-11-15",
    author: "Sarah Johnson",
    category: "Market Trends"
  },
  {
    id: 2,
    title: "Security Updates for Online Marketplaces",
    summary: "Important security measures all marketplace users should implement.",
    content: "With the rise in online scams, marketplace platforms are implementing enhanced security features including two-factor authentication and escrow services. Users are strongly advised to enable all available security options.",
    image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
    date: "2023-11-10",
    author: "Michael Rodriguez",
    category: "Security"
  },
  {
    id: 3,
    title: "Cryptocurrency Payment Integration Guide",
    summary: "How to set up cryptocurrency payments for your marketplace listings.",
    content: "This comprehensive guide walks sellers through the process of setting up cryptocurrency payment options. From wallet selection to transaction verification, we cover all the essentials for safe and efficient crypto transactions.",
    image: "https://images.unsplash.com/photo-1518544643590-3c98156d04d4?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
    date: "2023-11-05",
    author: "Alex Chen",
    category: "Payments"
  },
  {
    id: 4,
    title: "Top Seller Tips: Building Your Digital Storefront",
    summary: "Expert advice for creating an appealing and successful store.",
    content: "Successful marketplace sellers share their top strategies for creating a standout digital storefront. From product photography to customer service protocols, these proven techniques can help boost your sales and reputation.",
    image: "https://images.unsplash.com/photo-1472851294608-062f824d29cc?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
    date: "2023-10-28",
    author: "Jessica Wong",
    category: "Seller Tips"
  },
  {
    id: 5,
    title: "The Future of P2P Trading Platforms",
    summary: "Industry experts predict what's next for person-to-person marketplaces.",
    content: "As technology evolves, peer-to-peer trading platforms are expected to incorporate more AI-driven features, enhanced verification processes, and seamless cross-platform integrations. These innovations aim to make P2P trading more secure and user-friendly.",
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
    date: "2023-10-22",
    author: "David Parker",
    category: "Technology"
  },
  {
    id: 6,
    title: "User Protection Policies Updated",
    summary: "New guidelines to protect both buyers and sellers on the platform.",
    content: "Our marketplace has implemented updated user protection policies, including enhanced dispute resolution processes and automated fraud detection. These changes aim to create a safer trading environment for all members of our community.",
    image: "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800",
    date: "2023-10-15",
    author: "Amanda Lee",
    category: "Policy"
  },
];

// Extract unique categories
const categories = Array.from(new Set(newsItems.map(item => item.category)));

// News Card Component
const NewsCard = ({ news }: { news: NewsItem }) => {
  const [imageError, setImageError] = useState(false);
  
  // Get fallback image based on category or index
  const getFallbackImage = () => {
    const index = newsItems.findIndex(item => item.id === news.id);
    const fallbackIndex = index >= 0 && index < fallbackImages.length ? index : 0;
    return fallbackImages[fallbackIndex];
  };

  const handleReadMore = () => {
    console.log(`Reading more about: ${news.title}`);
    // Implementation for read more functionality
  };

  const handleExternalLink = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.stopPropagation();
    // External link handling
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/80 rounded-2xl overflow-hidden hover:shadow-2xl hover:shadow-teal-500/20 hover:border-teal-500/60 transition-all duration-300 flex flex-col h-full">
      {/* Image */}
      <div className="relative h-52 overflow-hidden bg-gray-900">
        {imageError ? (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900">
            <img 
              src={getFallbackImage()} 
              alt={news.title} 
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
            />
          </div>
        ) : (
          <img 
            src={news.image} 
            alt={news.title} 
            onError={handleImageError}
            className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
            loading="lazy"
          />
        )}
        <div className="absolute top-0 left-0 m-3">
          <span className="bg-teal-600/90 text-white text-xs font-medium px-3 py-1.5 rounded-full">
            {news.category}
          </span>
        </div>
      </div>
      
      {/* Content */}
      <div className="p-5 flex-grow">
        <h3 className="font-bold text-white text-xl mb-3 line-clamp-2 hover:text-teal-400 transition-colors">{news.title}</h3>
        <p className="text-gray-300 text-sm mb-4 line-clamp-2">{news.summary}</p>
        
        {/* Meta information */}
        <div className="flex justify-between items-center text-gray-400 text-xs mt-auto">
          <div className="flex items-center">
            <CalendarIcon size={14} className="mr-1.5" />
            <span>{new Date(news.date).toLocaleDateString()}</span>
          </div>
          <div className="flex items-center">
            <UserIcon size={14} className="mr-1.5" />
            <span>{news.author}</span>
          </div>
        </div>
      </div>
      
      {/* Footer */}
      <div className="bg-gray-900/60 px-5 py-3.5 flex justify-between items-center border-t border-gray-700/50">
        <button 
          className="text-sm font-semibold text-teal-400 hover:text-white transition-colors duration-300 flex items-center group"
          onClick={handleReadMore}
          aria-label={`Read more about ${news.title}`}
          tabIndex={0}
        >
          Read more
          <ChevronRightIcon size={16} className="ml-1 transform group-hover:translate-x-1 transition-transform duration-300" />
        </button>
        {news.url && (
          <a 
            href={news.url} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-gray-400 hover:text-white transition-colors p-1.5 rounded-full hover:bg-gray-800/50"
            aria-label="External link"
            onClick={handleExternalLink}
            tabIndex={0}
          >
            <ExternalLinkIcon size={18} />
          </a>
        )}
      </div>
    </div>
  );
};

// Main News Page Component
export default function News() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const filteredNews = newsItems.filter(news => {
    const matchesCategory = selectedCategory ? news.category === selectedCategory : true;
    const matchesSearch = searchQuery ? 
      news.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      news.summary.toLowerCase().includes(searchQuery.toLowerCase()) : true;
    
    return matchesCategory && matchesSearch;
  });

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(prev => prev === category ? null : category);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  const handleLoadMore = () => {
    setIsLoading(true);
    // Simulate loading more articles
    setTimeout(() => {
      setIsLoading(false);
      // In a real app, you would fetch more articles here
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-900 font-sans text-gray-300 p-4 sm:p-6 lg:p-8">
      <div className="container mx-auto">
        <div className="flex flex-col">
          {/* Main Content */}
          <main className="w-full flex flex-col xl:flex-row gap-8">
            {/* News List */}
            <div className="flex-grow">
              {/* Header and search/filter area */}
              <div className="mb-8 bg-gray-800/30 backdrop-blur-md p-6 rounded-2xl border border-gray-700/60">
                <header className="mb-6">
                  <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">Latest News</h1>
                  <p className="text-gray-400">Stay updated with the latest marketplace trends and announcements.</p>
                </header>

                {/* Search and filter bar */}
                <div className="flex flex-col sm:flex-row gap-4 items-stretch sm:items-center">
                  <div className="relative flex-grow">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <SearchIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search news..."
                      className="w-full py-2.5 pl-10 pr-4 bg-gray-900/70 border border-gray-700 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent text-gray-200 placeholder-gray-500 outline-none"
                      value={searchQuery}
                      onChange={handleSearch}
                    />
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {categories.map(category => (
                      <button
                        key={category}
                        onClick={() => handleCategorySelect(category)}
                        className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                          selectedCategory === category 
                            ? 'bg-teal-600 text-white' 
                            : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        }`}
                      >
                        {category}
                      </button>
                    ))}
                    {selectedCategory && (
                      <button 
                        className="px-3 py-1.5 rounded-lg text-sm font-medium bg-gray-700/50 text-gray-300 hover:bg-gray-700 transition-colors"
                        onClick={() => setSelectedCategory(null)}
                      >
                        Clear filter
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* News Grid - Updated to show 3 columns on desktop */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredNews.length > 0 ? (
                  filteredNews.map((news) => (
                    <NewsCard key={news.id} news={news} />
                  ))
                ) : (
                  <div className="col-span-full text-center py-12">
                    <p className="text-xl text-gray-400">No news articles found matching your criteria.</p>
                  </div>
                )}
              </div>
              
              {/* Load more button */}
              {filteredNews.length >= 6 && (
                <div className="mt-8 text-center">
                  <button 
                    onClick={handleLoadMore}
                    disabled={isLoading}
                    className="px-5 py-2.5 bg-teal-600 hover:bg-teal-700 text-white rounded-lg font-medium transition-colors disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center mx-auto"
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Loading...
                      </>
                    ) : "Load more articles"}
                  </button>
                </div>
              )}
            </div>

            {/* Ads Section - Same positioning as trustedSeller.tsx */}
            <div className="w-full xl:w-1/4 flex-shrink-0 flex flex-col sm:flex-row xl:flex-col gap-8">
              <div className="w-full sticky top-4"><AdCard /></div>
              <div className="w-full hidden sm:block"><AdCard /></div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
} 