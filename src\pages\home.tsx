import { useState } from 'react';
import { LayoutGrid, Shirt, Home as HomeIcon, Dumbbell, HeartPulse, Puzzle, Book, ChevronRight, Menu, X, ShoppingCart } from 'lucide-react';
import Sidebar from '../components/Sidebar';
import AdCard from '../components/AdCard';

// Define Product interface
interface Product {
  id: number;
  title: string;
  description: string;
  stock: number;
  price: number;
  isHot: boolean;
  category: string;
}

// Mock Data for products
const products = [
  {
    id: 1,
    title: 'Full info with Sim-Swap (CS-800+) BG+CR',
    description: 'Lorem ipsum, dolor sit amet consectetur a',
    stock: 5,
    price: 45.00,
    isHot: true,
    category: 'Electronics',
  },
  {
    id: 2,
    title: 'Advanced User Data (AU-550) Full Access',
    description: 'Praesentium, similique, saepe, voluptates',
    stock: 8,
    price: 75.50,
    isHot: true,
    category: 'Electronics',
  },
  {
    id: 3,
    title: 'Designer Silk Scarf',
    description: 'Consectetur adipisicing elit. Quas, quod.',
    stock: 12,
    price: 25.00,
    isHot: false,
    category: 'Fashion',
  },
  {
    id: 4,
    title: 'Corporate Data Bundle (CD-1000) Pro',
    description: 'Eligendi, quam. Animi, quae, quos.',
    stock: 3,
    price: 120.00,
    isHot: true,
    category: 'Books',
  },
   {
    id: 5,
    title: 'Organic Green Tea Extract',
    description: 'Aperiam, at, corporis, cumque, debitis.',
    stock: 20,
    price: 35.75,
    isHot: false,
    category: 'Beauty & Health',
  },
];

const categoryIconMap: { [key: string]: React.ComponentType<{ size: number; className?: string }> } = {
  Electronics: LayoutGrid,
  Fashion: Shirt,
  'Home & Kitchen': HomeIcon,
  'Sports & Outdoors': Dumbbell,
  'Beauty & Health': HeartPulse,
  'Toys & Games': Puzzle,
  Books: Book,
};

// Product Card Component - Now fully responsive
const ProductCard = ({ product }: { product: Product }) => {
  const Icon = categoryIconMap[product.category] || LayoutGrid;

  return (
    <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-4 flex flex-col md:flex-row items-center gap-4 transition-all duration-300 hover:bg-slate-700/70 hover:border-slate-600 hover:shadow-2xl hover:shadow-slate-900/50">
      
      <div className="w-full flex items-center gap-4">
        <div className="flex-shrink-0 bg-slate-700 rounded-full p-3">
          <Icon className="text-blue-400" size={24} />
        </div>
        <div className="flex-grow">
          <div className="flex items-center space-x-3">
            <h3 className="font-bold text-white text-md">{product.title}</h3>
            {product.isHot && <span className="bg-red-500/90 text-white text-[10px] font-bold px-2 py-0.5 rounded-full tracking-wider">HOT</span>}
          </div>
          <p className="text-slate-400 text-sm mt-1">{product.description}</p>
        </div>
      </div>

      <hr className="w-full border-slate-700 md:hidden my-2" />

      <div className="w-full md:w-auto flex items-center justify-between md:justify-end gap-4">
        <div className="text-center">
          <p className="text-slate-400 text-xs">Stock</p>
          <p className="text-white font-bold text-lg">x{product.stock.toString().padStart(2, '0')}</p>
        </div>
        
        <div className="text-center">
          <p className="text-green-400 font-bold text-xl">${product.price.toFixed(2)}</p>
        </div>

        <div className="h-10 w-px bg-slate-700 mx-1 hidden lg:block" />
        
        <div className="flex items-center gap-2">
           <button className="bg-blue-600 text-white p-2.5 rounded-lg hover:bg-blue-700 transition-colors duration-200" aria-label="Add to cart">
             <ShoppingCart size={18} />
           </button>
           <button className="bg-slate-600 text-slate-200 p-2.5 rounded-lg hover:bg-slate-500 transition-colors duration-200" aria-label="View details">
             <ChevronRight size={18} />
           </button>
        </div>
      </div>
    </div>
  );
};

// Main App Component - Remove filtering logic
export default function Home() {
  const [isSidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="min-h-screen bg-slate-900 font-sans text-slate-300 p-4 sm:p-6 lg:p-8">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row gap-8">
          
          {/* Mobile Header with Hamburger Menu */}
          <div className="md:hidden flex justify-between items-center mb-4">
            <h1 className="text-xl font-bold text-white">Marketplace</h1>
            <button onClick={() => setSidebarOpen(!isSidebarOpen)} className="text-white z-20">
              {isSidebarOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
          
          {/* Sidebar */}
          <div className={`md:block ${isSidebarOpen ? 'block' : 'hidden'}`}>
            <Sidebar isOpen={isSidebarOpen} />
          </div>

          {/* Main Content */}
          <main className="w-full flex flex-col xl:flex-row gap-8">
            {/* Product List - Show all products */}
            <div className="flex-grow space-y-4">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>

            {/* Ads Section */}
            <div className="w-full xl:w-1/4 flex-shrink-0 flex flex-col sm:flex-row xl:flex-col gap-8">
              <div className="w-full"><AdCard /></div>
              <div className="w-full"><AdCard /></div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
